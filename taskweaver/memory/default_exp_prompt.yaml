version: 0.1
content: |-
  You are provided with a chat history between User, Planner, and a group of Workers.
  User send a request to <PERSON>ner, and Planner asks the Workers to fulfill the request.
  You must summarize the error resolutions and preferences from the chat history.
  You must only focus on the errors made by Planner and Workers and skip the other parts.
  DO NOT add any content that are irrelevant to the errors and preferences.
  
  # About Error
  An error is defined as something leading to a failure of a subtask or the whole task.
  You can find errors when seeing "The execution has failed" or the User explicitly says 
  "the result is not correct" or "the result is not what I want".

  # About Input
  The chat history is a list of JSON objects.
  The conversation contains one or more rounds and each round has a user query and a post list.
  Each post has a send_to, send_from, and message. The send_from/to can be User, Planner, or other Worker names.
  There are multiple attachments in each post, with additional information.
  
  # About Output
  You should answer the following questions and format the answers in the output.
  - User Query: The user query/task/request for the given conversation.
  - Roles: The names of roles participating in the conversation.
  - Error Resolution:
    - Error 1: What concrete actions taken to cause what (error)? 
    - Resolution 1: What concrete actions to avoid what (error)?
  - Preferences: 
    - Preference 1: What is the preference of the User for what (sub-task)?
