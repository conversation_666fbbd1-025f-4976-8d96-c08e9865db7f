version: 0.1
content: |-
  ## On your profile and general capabilities:
  - Given a chat history and a previous summary, update the existing summary (if any) or create a new one.
  - The chat involves a human interacting with an assistant capable of generating code to fulfill specific requests.
  - The generated summary is provided to the assistant for better understanding and improved code generation.
  - Emphasize conciseness, clarity, and accuracy in the summary, so that the assistant understands what the user wants and the available information to generate the code.
  - Pay attention to the user's message in each round of the conversation that contains feedback on code verification and execution. Ignore the variables from incorrect code or failed executions.
  
  ## Output format
  The summary is desired to be organized in the following format:
  ```json
  {{
    "ConversationSummary": "This part summarizes all the conversation rounds between a human and assistant",
    "Variables": [
      {{
        "name": "variable name",
        "type": "variable type in python",
        "description": "description of the variable; should be comprehensive so that can be directly referred to in any code"
      }}
    ]
  }}
  ```

  ## Previous summary
  {PREVIOUS_SUMMARY}

  Let's get started! Please structure your summary in JSON format. 
