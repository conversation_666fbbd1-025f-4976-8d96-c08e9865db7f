# Welcome to *TaskWeaver* ! 

*Hi there, User! 👋 We're excited to have you on board.*

TaskWeaver is a code-first agent framework for seamlessly planning and executing data analytics tasks. This innovative framework interprets user requests through coded snippets and efficiently coordinates a variety of plugins in the form of functions to execute data analytics tasks. It supports key Features like: rich data structure, customized algorithms, incorporating domain-specific knowledge, stateful conversation, code verification, easy to use, debug and extend.

## Useful Links 🔗

- **Quick Start:** Quick start TaskWeaver with [README](https://github.com/microsoft/TaskWeaver?tab=readme-ov-file#-quick-start) ✨
- **Advanced Configurations:** Get started with our [TaskWeaver Documents](https://microsoft.github.io/TaskWeaver/) 📚
- **Technical Report:** Check out our [TaskWeaver Report](https://export.arxiv.org/abs/2311.17541) for more details! 📖
- **Discord Channel:** Join the TaskWeaver [Discord Channel](https://discord.gg/Z56MXmZgMb) for discussions 💬

We can't wait to see what you create with TaskWeaver!

**Start the Conversation!**
