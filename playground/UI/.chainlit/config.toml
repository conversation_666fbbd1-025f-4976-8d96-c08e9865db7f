[project]
# Whether to enable telemetry (default: true). No personal data is collected.
enable_telemetry = false

# List of environment variables to be provided by each user to use the app.
user_env = []

# Duration (in seconds) during which the session is saved when the connection is lost
session_timeout = 3600

# Enable third parties caching (e.g <PERSON><PERSON>hain cache)
cache = false

# Follow symlink for asset mount (see https://github.com/Chainlit/chainlit/issues/317)
# follow_symlink = true

[features]
# Show the prompt playground
prompt_playground = true

# Process and display HTML in messages. This can be a security risk (see https://stackoverflow.com/questions/19603097/why-is-it-dangerous-to-render-user-generated-html-or-javascript)
unsafe_allow_html = true

# Process and display mathematical expressions. This can clash with "$" characters in messages.
latex = false

# Authorize users to upload files with messages
spontaneous_file_upload.enabled = true

# Allows user to use speech to text
[features.speech_to_text]
    enabled = false
    # See all languages here https://github.com/JamesBrill/react-speech-recognition/blob/HEAD/docs/API.md#language-string
    # language = "en-US"

[UI]
# Name of the app and chatbot.
name = "TaskWeaver"

# Show the readme while the conversation is empty.
show_readme_as_default = true

# Description of the app and chatbot. This is used for HTML tags.
# description = "Chat with TaskWeaver"

# Large size content are by default collapsed for a cleaner ui
default_collapse_content = false

# The default value for the expand messages settings.
default_expand_messages = true

# Hide the chain of thought details from the user in the UI.
hide_cot = false

# Link to your github repo. This will add a github button in the UI's header.
# github = "https://github.com/microsoft/TaskWeaver"

# Specify a CSS file that can be used to customize the user interface.
# The CSS file can be served from the public directory or via an external link.
custom_css = "/public/style_v1.css"

# Override default MUI light theme. (Check theme.ts)
[UI.theme.light]
    #background = "#FAFAFA"
    #paper = "#FFFFFF"

    [UI.theme.light.primary]
        #main = "#F80061"
        #dark = "#980039"
        #light = "#FFE7EB"

# Override default MUI dark theme. (Check theme.ts)
[UI.theme.dark]
    #background = "#FAFAFA"
    #paper = "#FFFFFF"

    [UI.theme.dark.primary]
        #main = "#F80061"
        #dark = "#980039"
        #light = "#FFE7EB"


[meta]
generated_by = "0.7.700"
