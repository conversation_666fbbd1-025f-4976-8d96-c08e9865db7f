name: klarna_search
enabled: true
required: false
plugin_only: true
description: >-
  Search and compare prices from thousands of online shops. Only available in the US.
  This plugin only takes user requests when searching for merchandise.
  If not clear, confirm with the user if they want to search for merchandise from Klarna.
examples: |-
  result, description = klarna_search("laptop", 10, 1000, 2000)

parameters:
  - name: query
    type: str
    required: true
    description: >-
      A precise query that matches one very small category or product that needs to be searched for to find the products the user is looking for. 
      If the user explicitly stated what they want, use that as a query. 
      The query is as specific as possible to the product name or category mentioned by the user in its singular form, and don't contain any clarifiers like latest, newest, cheapest, budget, premium, expensive or similar. 
      The query is always taken from the latest topic, if there is a new topic a new query is started. 
      If the user speaks another language than English, translate their request into English (example: translate fia med knuff to ludo board game)!
  - name: size
    type: int
    required: false
    description: number of products to return
  - name: min_price
    type: int
    required: false
    description: (Optional) Minimum price in local currency for the product searched for. Either explicitly stated by the user or implicitly inferred from a combination of the user's request and the kind of product searched for.
  - name: max_price
    type: int
    required: false
    description: (Optional) Maximum price in local currency for the product searched for. Either explicitly stated by the user or implicitly inferred from a combination of the user's request and the kind of product searched for.

returns:
  - name: df
    type: DataFrame
    description: >-
      This DataFrame contains the search results.
  - name: description
    type: str
    description: This is a string describing the anomaly detection results.
