name: paper_summary
enabled: false
required: false
description: >-
  paper_summary function iteratively summarizes a given paper page by page,
  highlighting the key points, including the problem, main idea, contributions,
  experiments, results, and conclusions.
examples: |-
  result, description = paper_summary("paper.pdf")

parameters:
  - name: paper_file_path
    type: str
    required: true
    description: The file path of the paper to be summarized.

returns:
  - name: summary
    type: str
    description: The final summary of the paper after processing all pages.
  - name: description
    type: str
    description: A string describing the summarization process and the final summary.

configurations:
  api_type:
  api_base:
  api_key:
  api_version:
  deployment_name:
