enabled: True
rounds:
  - user_query: count the rows of /home/<USER>
    state: created
    post_list:
      - message: count the rows of /home/<USER>
        send_from: User
        send_to: Planner
        attachment_list:
      - message: Please load the data file /home/<USER>
        send_from: Planner
        send_to: CodeInterpreter
        attachment_list:
          - type: plan_reasoning
            content: |-
              The user wants to count the rows of the data file /home/<USER>
          - type: init_plan
            content: |-
              1. Load the data file
              2. Count the rows of the loaded data <sequentially depends on 1>
              3. Check the execution result and report the result to the user <interactively depends on 2>
          - type: plan
            content: |-
              1. Instruct CodeInterpreter to load the data file and count the rows of the loaded data
              2. Check the execution result and report the result to the user if it is correct
          - type: current_plan_step
            content: 1. Instruct CodeInterpreter to load the data file and count the rows of the loaded data
          - type: stop
            content: InProcess
      - message: Load the data file /home/<USER>
        send_from: CodeInterpreter
        send_to: Planner
        attachment_list:
      - message: The data file /home/<USER>
        send_from: Planner
        send_to: User
        attachment_list:
          - type: plan_reasoning
            content: |-
              The data file /home/<USER>
              The execution result is correct
              The user query is successfully answered
          - type: init_plan
            content: |-
              1. Load the data file
              2. Count the rows of the loaded data <sequentially depends on 1>
              3. Check the execution result and report the result to the user <interactively depends on 2>
          - type: plan
            content: |-
              1. Instruct CodeInterpreter to load the data file and count the rows of the loaded data
              2. Check the execution result and report the result to the user if it is correct
          - type: current_plan_step
            content: 2. report the result to the user
          - type: stop
            content: Completed
