[flake8]
ignore =
    # module level import not at top of file
    E402,
    # line break after binary operator
    W504,
    # line break before binary operator
    W503,
    # whitespace before ':'
    E203

exclude =
    .git,
    __pycache__,
    docs,
    build,
    dist,
    *.egg-info,
    docker_files,
    .vscode,
    .idea,
    .github,
    scripts,
    setup.py,
    workspaces

max-line-length = 120

per-file-ignores =
    # import not used: ignore in __init__.py files
    __init__.py:F401
