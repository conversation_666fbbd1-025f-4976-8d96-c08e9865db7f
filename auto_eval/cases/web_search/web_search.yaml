version: 0.1
config_var:
  execution_service.kernel_mode: "local"
  session.roles: ["planner", "web_search"]
  web_search.chunk_size: 2000
app_dir: ../project/
task_description: |-
  The task is to find the authors of the paper "TaskWeaver: A Code-First Agent Framework" and their affiliations.
  Then, the second step is to find the first author's home page.
scoring_points:
  - score_point: The author list should include at least <PERSON> who is the first author
    weight: 1
  - score_point: The affiliation list should include Microsoft
    weight: 1