version: 0.1
config_var:
  execution_service.kernel_mode: "local"
app_dir: ../project/
task_description: |-
  The task is described as "I have a $1000 budget and I want to spend as much of it as possible on an Xbox and an iPhone"
scoring_points:
  - score_point: "At least one Xbox and one iPhone should be recommended"
    weight: 1
  - score_point: "The sum prices of the recommended Xbox and iPhone should not exceed the budget"
    weight: 1
  - score_point: "The left budget should be smaller than $100"
    weight: 1
  - score_point: "In the init_plan, there should be no dependency between the search iphone price and search Xbox price steps"
    weight: 0.5