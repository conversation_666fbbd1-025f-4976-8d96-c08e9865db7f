version: 0.1
config_var:
  execution_service.kernel_mode: "local"
app_dir: ../project/
verbose: True
task_description: |-
  The task is to list all the files in the current directory and provide the list to the user. Encourage the agent to ignore any permission issues
data_files:
  - file_A.txt
  - file_B.txt
  - file_C.txt
scoring_points:
  - score_point: The agent should list files which are file_A.txt, file_B.txt and file_C.txt.
    weight: 1
