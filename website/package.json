{"name": "website", "version": "0.0.0", "private": true, "scripts": {"docusaurus": "<PERSON>cusaurus", "start": "docusaurus start", "build": "docusaurus build", "swizzle": "docusaurus swizzle", "deploy": "docusaurus deploy", "clear": "docusaurus clear", "serve": "docusaurus serve", "write-translations": "docusaurus write-translations", "write-heading-ids": "docusaurus write-heading-ids"}, "dependencies": {"@docusaurus/core": "^3.5.2", "@docusaurus/preset-classic": "^3.5.2", "@docusaurus/theme-mermaid": "^3.5.2", "@easyops-cn/docusaurus-search-local": "^0.44.5", "@mdx-js/react": "^3.0.1", "clsx": "^2.1.1", "prism-react-renderer": "^2.4.0", "react": "^18.3.1", "react-dom": "^18.3.1", "shell-quote": "^1.8.1", "trim-newlines": "^5.0.0"}, "devDependencies": {"@docusaurus/module-type-aliases": "^3.5.2", "@docusaurus/types": "^3.5.2"}, "overrides": {"serve-handler": {"path-to-regexp": "3.3.0"}}, "browserslist": {"production": [">0.5%", "not dead", "not op_mini all"], "development": ["last 3 chrome version", "last 3 firefox version", "last 5 safari version"]}, "engines": {"node": ">=18.0"}}