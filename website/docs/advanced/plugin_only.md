---
id: plugin_only
description: The Plugin Only Mode
slug: /plugin/plugin_only
---

# The Plugin-Only Mode

## What is the plugin-only mode?
The plugin-only mode is a restricted mode of TaskWeaver that only allows you to use plugins. 
Compared to the full mode, the plugin-only mode has the following restrictions:

1. The generated code only contains the calls to the plugins. 
   For example, the following code only calls the `ascii_render` plugin and does not contain any "free-form" code.
    ```python
    r1=ascii_render(**{"text":"Hello"})
    r1
    ```
    
2. Only the plugins with `plugin_only: true` in the yaml file will be loaded. 
   For example, the following plugin will be loaded in the plugin-only mode:
    ```yaml
    name: ascii_render
    plugin_only: true
    ...
    ```
   If this field is not specified, the default value is `false`.
   For plugins in the plugin-only mode, the argument type can only be `str`, `int`, `boolean`, or `float`.
   Other types such as `DataFrame` are not allowed.
   Essentially, we consider these plugins only produce "text-like" output that can be directly consumed by the LLM.

To support the plugin-only mode, we developed a new role called `PluginOrchestrator`.
To enable the plugin-only mode, you can add the configuration `"session.roles": ["planner", "code_interpreter_plugin_only"]` 
in the project configuration file `taskweaver_config.json`.

:::info
Although the plugin-only mode is restricted, it is still useful in some scenarios.
For example, you may want to use TaskWeaver to only generate the code to call a certain plugin, 
and you want to get the response from the plugin directly, without generating any other code 
for safety reasons.
:::

## How is the plugin-only mode implemented?

The plugin-only mode is implemented based on the [function calling](https://platform.openai.com/docs/guides/function-calling) mode of LLMs.
In this mode, the LLM is trained to generate a JSON object that contains the function name and the arguments.
For example, the following JSON object is generated by the LLM:
```json
{
  "function": "ascii_render",
  "arguments": {
    "text": "Hello"
  }
}
```
With this JSON object, we assemble the code to call the plugin:
```python
r1=ascii_render(**{"text":"Hello"})
r1
```
Then, we execute the code and get the response from the plugin.
Therefore, the code is not directly generated by the LLM in the plugin-only mode.

## Which models support the plugin-only mode?

Currently, the plugin-only mode is only supported by 

- [OpenAI models](https://platform.openai.com/docs/guides/function-calling)
- [ZhipuAI models](https://open.bigmodel.cn/dev/api)

Likely other models that are compatible with the OpenAI models will also support the plugin-only mode.