# FAQ

### Q: Why it takes so long to generate the output?

A: In the default setting, TaskWeaver typically goes through the following steps to generate the output:
1. The User sends a query to the Planner.
2. The Planner generates a plan based on the query and sends the plan to the CodeInterpreter.
3. The CodeInterpreter executes the plan and sends the execution result back to the Planner.
4. The Planner summarizes the execution result and sends the summary to the User.
5. The User receives the summary from the Planner.

In some cases, the Planer and CodeInterpreter can go back and forth multiple times to generate the output.
This process can take a long time, mostly due to the latency of calling the LLMs.
To speed up the process, you can try the following:
- Use a smaller LLM model, such as GPT-3.5 instead of GPT-4. However, you need to experiment with your use case to see if the smaller model can still generate the output correctly.
- Use the CodeInterpreter only mode, which skips the Planner and generates the code directly from the User query. This mode is faster because it skips the planning step. 
However, you should check if your use case needs the planning step.  

### Q: Why TaskWeaver fails and the logs say "Failed to connect to docker.daemon"?

A: This error typically happens when TaskWeaver is running in the `container` mode and cannot connect to the Docker daemon.
We have switched to the `container` mode by default to provide a more secure environment for code execution.
To opt out of the `container` mode, you can set the `execution_service.kernel_mode` parameter to `local` in the `taskweaver_config.json` file.
However, you should be aware that TaskWeaver can interact with the host machine directly in the `local` mode, which may have security risks.

### Q: Why I see errors saying the Planner failed to generate the `send_to`, `message` or other fields?

A: This is typically due to that the LLM failed to generate its output follow our schema. In Planner's prompt,
we asked the LLM to generate a JSON object that contains `send_to`, `message`, `init_plan`, `plan`, and `current_plan_step`.
Missing any of these fields will cause the parsing error. 
The most effective way to mitigate this issue is to use switch to more powerful LLM model, such as GPT-3.5 or GPT-4.

### Q: How do I know if TaskWeaver can see my plugin?

A: A simple way to check if TaskWeaver can see your plugin is to ask "What can you do?" to TaskWeaver.
The typical response is to list all the available plugins like the following:
```markdown
I can assist you with various tasks, including:

- Detecting anomalies in time series data.
- Rendering text into ASCII art.
- Searching and comparing prices from thousands of online shops (US only).
- Telling a joke.

If you have a specific task in mind, please let me know, and I'll do my best to assist you.
```
If you see your plugin in the list, it means TaskWeaver can see your plugin.
But this is not a reliable way to check if TaskWeaver can see your plugin because the response is generated by the LLM.
A more reliable way is to check the prompt of the Planner. You can find the prompts 
from `project/workspace/sessions/<session_id>/planner_prompt_log_xxxx.yaml`.
Then, search for this section as follows:

```markdown
CodeInterpreter has the following plugin functions and their required parameters need to be provided before the execution:
- anomaly_detection: anomaly_detection function identifies anomalies from an input DataFrame of time series. It will add a new column \"Is_Anomaly\", where each entry will be marked with \"True\" if the value is an anomaly or \"False\" otherwise. Arguments required: df: DataFrame, time_col_name: str, value_col_name: str
- ascii_render: This plugin renders the input text into ASCII art form. Arguments required: text: str
- klarna_search: Search and compare prices from thousands of online shops. Only available in the US. This plugin only takes user requests when searching for merchandise. If not clear, confirm with the user if they want to search for merchandise from Klarna. Arguments required: query: str
- tell_joke: Call this plugin to tell a joke.
```
Check if your plugin is in the list. If it is, it means TaskWeaver can see your plugin.

### Q: Why TaskWeaver cannot see my plugin?

First, make sure you have read our [Plugin Introduction](https://microsoft.github.io/TaskWeaver/docs/plugin/plugin_intro) and this tutorial carefully.
You should have two files in the `plugins` folder, e.g., `ascii_render.py` and `ascii_render.yaml`.

Now, if TaskWeaver cannot see your plugin, the root cause is typically syntax errors in the yaml file. 
Check the console output if you are using the command line interface, or the console logs if you are using the web interface.
You may see the following error message:
```bash
failed to loading component from <name>.yaml, skipping: Yaml loading failed due to: <reason>
```
The error message will tell you the reason why the yaml file cannot be loaded.
It is typically easy to fix the syntax errors by using a yaml linter (e.g., in Visual Studio Code) or an online yaml linter.

If you have checked the syntax of the yaml file and TaskWeaver still cannot see your plugin, please check
if the yaml file has included all the required fields such as the `parameters` and `returns` fields.


### Q: Why TaskWeaver can see my plugin but cannot call it?

A: In this case, you may see the generated code has called the plugin function, 
but the execution result is an error message saying that the plugin function is undefined.
If this happens, please check the console output if you are using the command line interface,
or the console logs if you are using the web interface.

You may see the following error message:
```bash
Plugin <name> failed to load: Plugin <name> failed to register: failed to load plugin <name> <reason>
```
This error message will tell you the reason why the plugin function cannot be loaded.
It is typically easy to fix the errors by checking the console output or logs.
The root cause is typically errors in the python file that causes the plugin function cannot be loaded.
Typical errors include syntax errors, missing imports, or missing packages.

Note that this sort of error is not caused by the implementation "inside" the plugin function.
Otherwise, the errors would be caught during the execution of the plugin function, 
not during the loading of the plugin function.

### Q: How to debug my plugin?

A: We are working on a debugging tool to help you debug your plugin. For now, a simple way to debug your plugin is to 
define a main function in the python file and run it in your local environment.
For example, you can define a main function in `ascii_render.py` as follows:
```python
if __name__ == "__main__":
    from taskweaver.plugin.context import temp_context

    with temp_context() as temp_ctx:
        render = AsciiRenderPlugin(name="ascii_render", ctx=temp_ctx, config={})
        print(render(text="hello world!"))
```
In this main function, we create a temporary context and call the plugin function with some input.
You need not change the plugin implementation. Just add the main function to the end of the python file.
Then, run the python file in your local environment. If there are any errors, you can see them in the console output.

If you have the `configurations` section in the yaml file, you can manually set the configurations in the `config` parameter of the plugin constructor.
We currently do not read the yaml file, so you need to make sure that the configurations are set correctly in the `config` parameter.
For example, if an integer configuration `max_length` is defined in the yaml file, you can set it in the `config` parameter as follows:
```python
config = {
    "max_length": 100
}
```
Then, pass the `config` to the plugin constructor. As yaml is type sensitive, you need to make sure that the type of the configuration is correct.


### Q: Why I see the error message "RuntimeError: This event loop is already running"?

A: We use a Jupyter Kernel to execute the code in TaskWeaver. The Jupyter Kernel uses an event loop to manage the execution of the code.
If you see the error message "RuntimeError: This event loop is already running.", it typically means that the event loop is already running.
This is typically caused by the environment where TaskWeaver is running. For example, if you are running TaskWeaver in a Jupyter Notebook.

### Q: Why I see the error message "Permissions assignment failed for secure file: Got '0o677' instead of '0o0600'." when running TaskWeaver?

A: This error message typically happens when TaskWeaver is running in the container mode on a Windows machine with WSL.
One can refer to this [issue](https://github.com/jupyter/notebook/issues/5428) for more details.
The fix is to set `JUPYTER_ALLOW_INSECURE_WRITES=true` for the container.