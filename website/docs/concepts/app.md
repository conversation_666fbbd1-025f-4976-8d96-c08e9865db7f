# Application

The `TaskWeaverApp` is the main class of the TaskWeaver library. It is the entry point for the library and is used to create and run tasks.
A `TaskWeaverApp` instance is created by calling the `TaskWeaverApp.__init__` constructor.

```python
class TaskWeaverApp(object):
    def __init__(
        self,
        app_dir: Optional[str] = None,
        use_local_uri: Optional[bool] = None,
        config: Optional[Dict[str, Any]] = None,
        **kwargs: Any,
    ) -> None:
        """
        Initialize the TaskWeaver app.
        :param app_dir: The project directory.
        :param use_local_uri: Whether to use local URI for artifacts.
        :param config: The configuration.
        :param kwargs: The additional arguments.
        """
```

The `app_dir` parameter is the path to the [project](project.md) directory. 
The `use_local_uri` parameter is a boolean that specifies whether to use local URIs for artifacts. 
If the artifacts are stored locally, the `use_local_uri` parameter should be set to `True`.
This is useful if TaskWeaver is being used as a remote service and the artifacts are then remote URIs.
The `config` parameter is a dictionary that contains the configuration settings for the app.
Any settings in the `config` parameter will override existing settings in the configuration file or environment variables.

The `TaskWeaverApp` class has the following methods:

```python
def get_session(
    self,
    session_id: Optional[str] = None,
) -> Session:
    """
    Get the session. Return a new session if the session ID is not provided.
    :param session_id: The session ID.
    :return: The session.
    """
```

The `get_session` method is used to get a session. If the `session_id` parameter is not provided, a new session is created.
A session is a conversation instance that the user has with the TaskWeaver app.
We shall discuss the `Session` class [later](session.md).

```python
def stop(self) -> None:
    """
    Stop the TaskWeaver app. This function must be called before the app exits.
    """
```

The `stop` method is used to stop the TaskWeaver app. This method must be called before the app exits.

An example of creating a `TaskWeaverApp` instance is shown below:

```python
from taskweaver.app.app import TaskWeaverApp

app = TaskWeaverApp(app_dir="path/to/project/")
session = app.get_session()
round = session.send_message("Hello, how can I help you?")

print(round)
app.stop()
```

In this example, a `TaskWeaverApp` instance is created with the project directory set to `"path/to/project/"`.
A session is then created using the `get_session` method, and a message is sent to the session.
The response will be the entire conversation [round](round.md) that was generated by the TaskWeaver app.
Finally, the `stop` method is called to stop the TaskWeaver app.

