name: Python package

on:
  push:
    branches:    
      - main
  pull_request:
    branches:    
      - main

jobs:
  pytest:

    runs-on: ubuntu-latest
    strategy:
      matrix:
        python-version: ["3.11"]

    steps:
      - uses: actions/checkout@v4
      - name: Set up Python ${{ matrix.python-version }}
        uses: actions/setup-python@v5
        with:
          python-version: ${{ matrix.python-version }}
      - name: Display Python version
        run: python -c "import sys; print(sys.version)"
      - name: Install taskweaver
        run: |
          python -m pip install --upgrade pip setuptools wheel
          pip install -e .
      - name: Test with pytest
        run: |
          pip install pytest pytest-cov
          pytest tests/unit_tests --collect-only
          pytest tests/unit_tests -v --junitxml=junit/test-results-${{ matrix.python-version }}.xml --cov=com --cov-report=xml --cov-report=html
      - name: Upload pytest test results
        uses: actions/upload-artifact@v4
        with:
          name: pytest-results-${{ matrix.python-version }}
          path: junit/test-results-${{ matrix.python-version }}.xml
        # Use always() to always run this step to publish test results when there are test failures
        if: ${{ always() }}
        
