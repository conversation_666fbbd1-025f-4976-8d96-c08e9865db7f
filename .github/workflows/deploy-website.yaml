name: docs

on:
  push:
    branches: [main]
    path:
      - 'website/*'
      - '.github/workflows/deploy-website.yml'
  workflow_dispatch:
  merge_group:
    types: [checks_requested]

jobs:
  gh-release:
    runs-on: ubuntu-latest
    permissions:
      contents: write
    defaults:
      run:
        working-directory: website
    steps:
      - uses: actions/checkout@v4
      - name: Use Node.js
        uses: actions/setup-node@v4
        with:
          node-version: 20.x
      - name: Build website
        run: |
          if [ -e yarn.lock ]; then
          yarn install --frozen-lockfile --ignore-engines
          yarn build
          elif [ -e package-lock.json ]; then
          npm ci
          npm run build
          else
          npm i --legacy-peer-deps
          npm run build
          fi
      - name: Deploy to GitHub Pages
        uses: peaceiris/actions-gh-pages@v4
        with:
          github_token: ${{ secrets.GITHUB_TOKEN }}
          # Build output to publish to the `gh-pages` branch:
          publish_dir: ./website/build