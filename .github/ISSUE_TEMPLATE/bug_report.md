---
name: Bug report
about: Create a report to help us improve
title: ''
labels: ''
assignees: ''

---

**Describe the bug**
A clear and concise description of what the bug is.

**To Reproduce**
Steps to reproduce the behavior:
1. Start the service
2. Type the user query "xxx"
3. Wait for the response
4. Type the user query "yyy"
4. See error

**Expected behavior**
A clear and concise description of what you expected to happen. NA if feel not applicable.

**Screenshots**
If applicable, add screenshots to help explain your problem.

**Environment Information (please complete the following information):**
 - OS: [e.g. Linux, Windows, WSL]
 - Python Version [e.g. 3.10, 3.11]
 - LLM that you're using: [e.g., GPT-4]
 - Other Configurations except the LLM api/key related: [e.g., code_verification: true]

**Additional context**
Add any other context about the problem here.
